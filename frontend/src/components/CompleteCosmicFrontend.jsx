import React, { useState, useEffect, useRef } from 'react';
import { 
  MessageCircle, 
  Code2, 
  Library, 
  Settings, 
  Moon, 
  Sun, 
  Plus, 
  ChevronLeft,
  ChevronRight,
  Play,
  Brain,
  Cpu,
  Zap,
  Sparkles,
  Send,
  User,
  Bot,
  Copy,
  Download,
  Upload,
  Trash2,
  Search,
  Star,
  BookOpen,
  Maximize2,
  Minimize2,
  Check,
  AlertCircle,
  Orbit,
  RefreshCw
} from 'lucide-react';

// API Service for backend communication
class CosmicAPI {
  constructor() {
    this.baseURL = 'http://localhost:8001';
  }

  async request(endpoint, options = {}) {
    const url = `${this.baseURL}${endpoint}`;
    const config = {
      headers: {
        'Content-Type': 'application/json',
        ...options.headers,
      },
      ...options,
    };

    try {
      const response = await fetch(url, config);
      if (!response.ok) {
        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
      }
      return await response.json();
    } catch (error) {
      console.error(`API Error [${endpoint}]:`, error);
      throw error;
    }
  }

  // Chat API methods
  async createChatSession(title, modelName) {
    return this.request('/api/v1/chat/sessions', {
      method: 'POST',
      body: JSON.stringify({ title, model_name: modelName }),
    });
  }

  async getChatSessions() {
    return this.request('/api/v1/chat/sessions');
  }

  async getChatSession(sessionId) {
    return this.request(`/api/v1/chat/sessions/${sessionId}`);
  }

  async getSessionMessages(sessionId) {
    return this.request(`/api/v1/chat/sessions/${sessionId}/messages`);
  }

  async sendMessage(sessionId, content) {
    return this.request(`/api/v1/chat/sessions/${sessionId}/messages`, {
      method: 'POST',
      body: JSON.stringify({ content }),
    });
  }

  async deleteChatSession(sessionId) {
    return this.request(`/api/v1/chat/sessions/${sessionId}`, {
      method: 'DELETE',
    });
  }

  // Code execution methods
  async executeCode(code, language, files = null, sessionId = null) {
    return this.request('/api/v1/execute/run', {
      method: 'POST',
      body: JSON.stringify({ code, language, files, session_id: sessionId }),
    });
  }

  async validateCode(code, language) {
    return this.request('/api/v1/execute/validate', {
      method: 'POST',
      body: JSON.stringify({ code, language }),
    });
  }

  async getExecutionHistory(sessionId = null, limit = 50) {
    const params = new URLSearchParams({ limit: limit.toString() });
    if (sessionId) params.append('session_id', sessionId.toString());
    return this.request(`/api/v1/execute/history?${params}`);
  }

  async getSandboxFiles() {
    return this.request('/api/v1/execute/sandbox/files');
  }

  async cleanSandbox() {
    return this.request('/api/v1/execute/sandbox/clean', { method: 'DELETE' });
  }

  async getSupportedLanguages() {
    return this.request('/api/v1/execute/languages');
  }

  // Model management
  async getAvailableModels() {
    return this.request('/api/v1/models/available');
  }

  async refreshModels() {
    return this.request('/api/v1/models/refresh', { method: 'POST' });
  }

  async getSuggestedModels() {
    return this.request('/api/v1/models/suggest');
  }

  // Health check
  async healthCheck() {
    return this.request('/health');
  }

  // WebSocket connection
  createWebSocket(sessionId) {
    const wsUrl = `ws://localhost:8001/api/v1/chat/sessions/${sessionId}/ws`;
    return new WebSocket(wsUrl);
  }
}

const api = new CosmicAPI();

// Cosmic Layout Component
function CosmicLayout({ 
  children, 
  theme, 
  onThemeToggle, 
  sidebarCollapsed, 
  onSidebarToggle,
  sessions,
  currentSession,
  onSessionSwitch,
  onNewSession,
  currentMode = 'chat',
  onModeChange 
}) {
  const [mousePosition, setMousePosition] = useState({ x: 0, y: 0 });
  const [mouseVelocity, setMouseVelocity] = useState(0);
  const canvasRef = useRef(null);
  const trailCanvasRef = useRef(null);
  const particlesRef = useRef([]);
  const lastMousePos = useRef({ x: 0, y: 0 });
  const trailPoints = useRef([]);

  const navigationItems = [
    {
      id: 'chat',
      name: 'Neural Chat',
      icon: MessageCircle,
      description: 'AI Consciousness Interface',
      color: 'from-cyan-400 to-blue-500'
    },
    {
      id: 'code',
      name: 'Quantum Editor',
      icon: Code2,
      description: 'Reality Manipulation Chamber',
      color: 'from-purple-400 to-pink-500'
    },
    {
      id: 'snippets',
      name: 'Memory Bank',
      icon: Library,
      description: 'Code DNA Repository',
      color: 'from-emerald-400 to-teal-500'
    }
  ];

  // Cosmic mouse trail system
  useEffect(() => {
    const trailCanvas = trailCanvasRef.current;
    if (!trailCanvas) return;
    
    const ctx = trailCanvas.getContext('2d');
    const updateCanvasSize = () => {
      trailCanvas.width = window.innerWidth;
      trailCanvas.height = window.innerHeight;
    };
    
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    const animateTrail = () => {
      ctx.fillStyle = 'rgba(0, 0, 0, 0.05)';
      ctx.fillRect(0, 0, trailCanvas.width, trailCanvas.height);
      
      if (trailPoints.current.length > 1) {
        ctx.lineCap = 'round';
        ctx.lineJoin = 'round';
        
        for (let layer = 0; layer < 3; layer++) {
          ctx.beginPath();
          
          for (let i = 0; i < trailPoints.current.length - 1; i++) {
            const point = trailPoints.current[i];
            const nextPoint = trailPoints.current[i + 1];
            
            if (!point || !nextPoint) continue;
            
            const age = Date.now() - point.time;
            const maxAge = 1500;
            const opacity = Math.max(0, 1 - age / maxAge);
            
            if (opacity <= 0) continue;
            
            const velocityIntensity = Math.min(point.velocity / 15, 1);
            const hue = 180 + velocityIntensity * 80 + layer * 15;
            const saturation = 70 + velocityIntensity * 30;
            const lightness = 40 + layer * 20;
            
            const baseWidth = (1.5 + velocityIntensity * 6) * (3 - layer);
            ctx.lineWidth = baseWidth * opacity;
            
            const gradient = ctx.createLinearGradient(point.x, point.y, nextPoint.x, nextPoint.y);
            gradient.addColorStop(0, `hsla(${hue}, ${saturation}%, ${lightness}%, ${opacity * 0.6})`);
            gradient.addColorStop(1, `hsla(${hue + 20}, ${saturation}%, ${lightness + 10}%, ${opacity * 0.2})`);
            
            ctx.strokeStyle = gradient;
            
            if (i === 0) {
              ctx.moveTo(point.x, point.y);
            } else {
              const cp1x = (point.x + nextPoint.x) / 2;
              const cp1y = (point.y + nextPoint.y) / 2;
              ctx.quadraticCurveTo(point.x, point.y, cp1x, cp1y);
            }
          }
          
          ctx.stroke();
        }
      }
      
      const now = Date.now();
      trailPoints.current = trailPoints.current.filter(point => now - point.time < 1500);
      
      requestAnimationFrame(animateTrail);
    };
    
    animateTrail();
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // Particle system
  useEffect(() => {
    const canvas = canvasRef.current;
    if (!canvas) return;
    
    const ctx = canvas.getContext('2d');
    const updateCanvasSize = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    updateCanvasSize();
    window.addEventListener('resize', updateCanvasSize);

    particlesRef.current = [];
    for (let i = 0; i < 60; i++) {
      particlesRef.current.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        vx: (Math.random() - 0.5) * 1.5,
        vy: (Math.random() - 0.5) * 1.5,
        size: Math.random() * 2 + 0.5,
        color: `hsl(${Math.random() * 60 + 200}, 70%, 60%)`,
        pulse: Math.random() * Math.PI * 2
      });
    }

    const animate = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      particlesRef.current.forEach((particle, i) => {
        particle.x += particle.vx;
        particle.y += particle.vy;
        particle.pulse += 0.05;
        
        if (particle.x < 0) particle.x = canvas.width;
        if (particle.x > canvas.width) particle.x = 0;
        if (particle.y < 0) particle.y = canvas.height;
        if (particle.y > canvas.height) particle.y = 0;
        
        const pulseSize = Math.max(0.5, particle.size + Math.sin(particle.pulse) * 0.8);
        const alpha = Math.max(0.1, 0.3 + Math.sin(particle.pulse) * 0.2);
        
        ctx.beginPath();
        ctx.arc(particle.x, particle.y, pulseSize, 0, Math.PI * 2);
        ctx.fillStyle = particle.color + Math.floor(alpha * 100).toString(16).padStart(2, '0');
        ctx.fill();
        
        particlesRef.current.slice(i + 1).forEach(otherParticle => {
          const dx = particle.x - otherParticle.x;
          const dy = particle.y - otherParticle.y;
          const distance = Math.sqrt(dx * dx + dy * dy);
          
          if (distance < 80) {
            ctx.beginPath();
            ctx.moveTo(particle.x, particle.y);
            ctx.lineTo(otherParticle.x, otherParticle.y);
            ctx.strokeStyle = `rgba(100, 200, 255, ${0.15 * (1 - distance / 80)})`;
            ctx.lineWidth = 0.5;
            ctx.stroke();
          }
        });
      });
      
      requestAnimationFrame(animate);
    };
    
    animate();
    return () => window.removeEventListener('resize', updateCanvasSize);
  }, []);

  // Enhanced mouse tracking
  useEffect(() => {
    const handleMouseMove = (e) => {
      const newPos = { x: e.clientX, y: e.clientY };
      
      const dx = newPos.x - lastMousePos.current.x;
      const dy = newPos.y - lastMousePos.current.y;
      const velocity = Math.sqrt(dx * dx + dy * dy);
      
      setMousePosition(newPos);
      setMouseVelocity(velocity);
      
      trailPoints.current.push({
        x: newPos.x,
        y: newPos.y,
        time: Date.now(),
        velocity: velocity
      });
      
      if (trailPoints.current.length > 60) {
        trailPoints.current = trailPoints.current.slice(-30);
      }
      
      lastMousePos.current = newPos;
    };
    
    window.addEventListener('mousemove', handleMouseMove);
    return () => window.removeEventListener('mousemove', handleMouseMove);
  }, []);

  const handleModeChange = (mode) => {
    if (onModeChange) onModeChange(mode);
  };

  const getCurrentModeInfo = () => {
    return navigationItems.find(item => item.id === currentMode) || navigationItems[0];
  };

  return (
    <div className="relative min-h-screen bg-black overflow-hidden">
      <canvas ref={canvasRef} className="fixed inset-0 pointer-events-none z-0" />
      <canvas ref={trailCanvasRef} className="fixed inset-0 pointer-events-none z-10" />
      
      <div 
        className="fixed inset-0 opacity-20 z-15"
        style={{
          background: `radial-gradient(circle at ${mousePosition.x}px ${mousePosition.y}px, 
            rgba(59, 130, 246, ${0.25 + mouseVelocity * 0.01}) 0%, 
            rgba(147, 51, 234, ${0.15 + mouseVelocity * 0.008}) 30%, 
            rgba(236, 72, 153, ${0.08 + mouseVelocity * 0.005}) 60%, 
            transparent 80%)`
        }}
      />

      <div className="relative z-20 flex h-screen">
        {/* Cosmic Sidebar */}
        <div className={`${
          sidebarCollapsed ? 'w-16' : 'w-80'
        } bg-gray-900/80 backdrop-blur-xl border-r border-cyan-500/20 shadow-2xl transition-all duration-300 flex flex-col`}>
          
          <div className="p-4 border-b border-gray-700/50">
            <div className="flex items-center justify-between">
              {!sidebarCollapsed && (
                <div className="flex items-center space-x-3">
                  <div className="relative">
                    <div className="w-10 h-10 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-xl flex items-center justify-center">
                      <Brain className="text-white" size={20} />
                    </div>
                    <div className="absolute inset-0 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-xl blur-lg opacity-30 animate-pulse"></div>
                  </div>
                  <div>
                    <h1 className="text-xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                      LocalLLM Studio
                    </h1>
                    <p className="text-xs text-gray-400">Neural Development Environment</p>
                  </div>
                </div>
              )}
              <button
                onClick={onSidebarToggle}
                className="p-2 text-gray-400 hover:text-cyan-400 rounded-lg hover:bg-gray-800/50 transition-all"
              >
                {sidebarCollapsed ? <ChevronRight size={20} /> : <ChevronLeft size={20} />}
              </button>
            </div>
          </div>

          <div className="p-4 border-b border-gray-700/50">
            {!sidebarCollapsed ? (
              <div className="space-y-3">
                <h3 className="text-sm font-medium text-gray-300 flex items-center space-x-2">
                  <Cpu size={16} className="text-cyan-400" />
                  <span>Neural Interfaces</span>
                </h3>
                
                <div className="space-y-2">
                  {navigationItems.map((item) => {
                    const Icon = item.icon;
                    const isActive = currentMode === item.id;
                    
                    return (
                      <button
                        key={item.id}
                        onClick={() => handleModeChange(item.id)}
                        className={`w-full p-3 rounded-xl text-left transition-all duration-300 group relative overflow-hidden ${
                          isActive
                            ? 'bg-gradient-to-r ' + item.color + ' text-white shadow-lg'
                            : 'bg-gray-800/30 text-gray-300 hover:bg-gray-700/50 border border-gray-700/30'
                        }`}
                      >
                        {isActive && (
                          <div className="absolute inset-0 bg-gradient-to-r from-cyan-400/20 to-purple-400/20 animate-pulse"></div>
                        )}
                        <div className="relative flex items-center space-x-3">
                          <Icon size={18} />
                          <div>
                            <div className="font-medium text-sm">{item.name}</div>
                            <div className="text-xs opacity-75">{item.description}</div>
                          </div>
                        </div>
                      </button>
                    );
                  })}
                </div>
              </div>
            ) : (
              <div className="space-y-2">
                {navigationItems.map((item) => {
                  const Icon = item.icon;
                  const isActive = currentMode === item.id;
                  
                  return (
                    <button
                      key={item.id}
                      onClick={() => handleModeChange(item.id)}
                      className={`w-full p-3 rounded-xl transition-all ${
                        isActive
                          ? 'bg-gradient-to-r ' + item.color + ' text-white'
                          : 'text-gray-400 hover:text-cyan-400 hover:bg-gray-800/50'
                      }`}
                      title={item.name}
                    >
                      <Icon size={18} />
                    </button>
                  );
                })}
              </div>
            )}
          </div>

          <div className="flex-1 overflow-y-auto">
            {currentMode === 'chat' && !sidebarCollapsed && (
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <h3 className="text-sm font-medium text-gray-300">Neural Sessions</h3>
                  <button
                    onClick={onNewSession}
                    className="p-1.5 text-cyan-400 hover:text-cyan-300 rounded-lg hover:bg-gray-800/50 transition-all"
                    title="New Session"
                  >
                    <Plus size={16} />
                  </button>
                </div>
                
                <div className="space-y-2">
                  {sessions?.map((session) => (
                    <button
                      key={session.id}
                      onClick={() => onSessionSwitch(session.id)}
                      className={`w-full p-3 text-left rounded-lg transition-all ${
                        currentSession?.id === session.id
                          ? 'bg-cyan-500/20 text-cyan-300 border border-cyan-500/30'
                          : 'text-gray-400 hover:text-gray-200 hover:bg-gray-800/30'
                      }`}
                    >
                      <div className="font-medium text-sm truncate">{session.title}</div>
                      <div className="text-xs opacity-75 flex items-center space-x-2">
                        <span>{session.model_name}</span>
                        <span>•</span>
                        <span>{new Date(session.updated_at).toLocaleDateString()}</span>
                      </div>
                    </button>
                  ))}
                  
                  {(!sessions || sessions.length === 0) && (
                    <div className="text-center py-8 text-gray-500">
                      <Brain size={24} className="mx-auto mb-2 opacity-50" />
                      <p className="text-sm mb-2">No neural sessions</p>
                      <button
                        onClick={onNewSession}
                        className="text-cyan-400 hover:text-cyan-300 text-sm transition-colors"
                      >
                        Initialize consciousness
                      </button>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>

          <div className="p-4 border-t border-gray-700/50">
            {!sidebarCollapsed ? (
              <div className="space-y-3">
                <div className="bg-gray-800/50 rounded-lg p-3">
                  <div className="flex items-center space-x-2 mb-1">
                    <Sparkles size={16} className="text-purple-400" />
                    <span className="text-sm font-medium text-gray-200">
                      {getCurrentModeInfo().name}
                    </span>
                  </div>
                  <p className="text-xs text-gray-400">{getCurrentModeInfo().description}</p>
                </div>
                
                <button
                  onClick={onThemeToggle}
                  className="w-full flex items-center space-x-3 p-2 text-gray-300 hover:text-cyan-400 hover:bg-gray-800/50 rounded-lg transition-all"
                >
                  {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
                  <span>{theme === 'dark' ? 'Light Reality' : 'Dark Reality'}</span>
                </button>
              </div>
            ) : (
              <div className="space-y-2">
                <button
                  onClick={onThemeToggle}
                  className="w-full p-2 text-gray-400 hover:text-cyan-400 rounded-lg transition-all"
                  title={theme === 'dark' ? 'Light Reality' : 'Dark Reality'}
                >
                  {theme === 'dark' ? <Sun size={18} /> : <Moon size={18} />}
                </button>
              </div>
            )}
          </div>
        </div>

        {/* Main Content */}
        <div className="flex-1 flex flex-col overflow-hidden">
          <div className="bg-gray-900/70 backdrop-blur-xl border-b border-gray-700/50 px-6 py-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <h2 className="text-xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
                  {getCurrentModeInfo().name}
                </h2>
                
                {currentMode === 'chat' && currentSession && (
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                    <span>{currentSession.title}</span>
                    <span>•</span>
                    <span>{currentSession.model_name}</span>
                  </div>
                )}
                
                {currentMode === 'code' && (
                  <div className="flex items-center space-x-2 text-sm text-gray-400">
                    <Zap size={16} className="text-yellow-400" />
                    <span>Reality manipulation active</span>
                  </div>
                )}
              </div>
              
              <div className="flex items-center space-x-3">
                {currentMode === 'code' && (
                  <div className="flex items-center space-x-2 text-sm text-gray-500">
                    <Play size={16} />
                    <span>Ctrl+Enter to execute</span>
                  </div>
                )}
                
                <div className="flex items-center space-x-1">
                  <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
                  <span className="text-xs text-gray-400">Neural link active</span>
                </div>
              </div>
            </div>
          </div>

          <div className="flex-1 overflow-hidden bg-gray-900/20 backdrop-blur-sm">
            {children}
          </div>
        </div>
      </div>
    </div>
  );
}

// Enhanced Chat Interface with Real Backend
function CosmicChatInterface({ session, onExecuteCode }) {
  const [message, setMessage] = useState('');
  const [messages, setMessages] = useState([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingContent, setStreamingContent] = useState('');
  const [copied, setCopied] = useState(false);
  const [models, setModels] = useState([]);
  const [currentModel, setCurrentModel] = useState(null);
  const messagesEndRef = useRef(null);

  // Load models on mount
  useEffect(() => {
    loadModels();
  }, []);

  // Load messages when session changes
  useEffect(() => {
    if (session) {
      loadMessages();
    }
  }, [session]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages, streamingContent]);

  const loadModels = async () => {
    try {
      const response = await api.getAvailableModels();
      setModels(response.models || []);
      if (response.models && response.models.length > 0) {
        setCurrentModel(response.models[0]);
      }
    } catch (error) {
      console.error('Failed to load models:', error);
    }
  };

  const loadMessages = async () => {
    if (!session) return;
    
    try {
      const sessionMessages = await api.getSessionMessages(session.id);
      setMessages(sessionMessages || []);
    } catch (error) {
      console.error('Failed to load messages:', error);
    }
  };

  const handleSend = async () => {
    if (!message.trim() || isLoading || !session) return;
    
    setIsLoading(true);
    const userMessage = message;
    setMessage('');
    
    // Add user message to UI immediately
    const tempMessage = {
      id: Date.now(),
      role: 'user',
      content: userMessage,
      timestamp: new Date().toISOString()
    };
    setMessages(prev => [...prev, tempMessage]);
    
    try {
      // Send message to backend
      await api.sendMessage(session.id, userMessage);
      
      // Wait a moment for the AI response to be generated
      setTimeout(async () => {
        await loadMessages(); // Reload all messages to get the AI response
      }, 2000);
      
    } catch (error) {
      console.error('Failed to send message:', error);
      // Add error message
      const errorMessage = {
        id: Date.now() + 1,
        role: 'assistant',
        content: `Error: ${error.message}. Please check if the backend is running on http://localhost:8000`,
        timestamp: new Date().toISOString()
      };
      setMessages(prev => [...prev, errorMessage]);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCopy = async (text) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy text:', err);
    }
  };

  const handleExecuteCode = async (code, language) => {
    if (onExecuteCode) {
      try {
        return await onExecuteCode(code, language);
      } catch (error) {
        return {
          status: 'failed',
          error: error.message,
          execution_time: 0
        };
      }
    }
  };

  const CosmicCodeBlock = ({ code, language }) => {
    const [executing, setExecuting] = useState(false);
    const [result, setResult] = useState(null);

    const executeCode = async () => {
      setExecuting(true);
      try {
        const execResult = await handleExecuteCode(code, language);
        setResult(execResult);
      } catch (error) {
        setResult({ status: 'failed', error: error.message });
      } finally {
        setExecuting(false);
      }
    };

    return (
      <div className="relative group my-4">
        <div className="flex items-center justify-between bg-gray-800/90 backdrop-blur-sm px-4 py-2 rounded-t-lg border border-cyan-500/20">
          <span className="text-sm font-medium text-cyan-400 flex items-center space-x-2">
            <Code2 size={16} />
            <span>{language}</span>
          </span>
          <div className="flex items-center space-x-2 opacity-0 group-hover:opacity-100 transition-all duration-300">
            <button
              onClick={() => handleCopy(code)}
              className="p-1.5 text-gray-400 hover:text-cyan-400 transition-colors rounded"
              title="Copy code"
            >
              {copied ? <Check size={16} /> : <Copy size={16} />}
            </button>
            {['python', 'javascript', 'html', 'css'].includes(language) && (
              <button
                onClick={executeCode}
                disabled={executing}
                className="p-1.5 text-emerald-400 hover:text-emerald-300 transition-colors disabled:opacity-50 rounded"
                title="Execute code"
              >
                {executing ? (
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-emerald-400" />
                ) : (
                  <Play size={16} />
                )}
              </button>
            )}
          </div>
        </div>
        
        <div className="bg-gray-900/95 backdrop-blur-sm border border-cyan-500/20 border-t-0 rounded-b-lg overflow-hidden">
          <pre className="p-4 text-sm text-gray-100 overflow-x-auto">
            <code>{code}</code>
          </pre>
          
          {result && (
            <div className="border-t border-gray-700/50 bg-gray-800/50 p-4">
              <div className="flex items-center space-x-2 mb-2">
                <Zap size={16} className="text-yellow-400" />
                <span className="text-sm font-medium text-gray-200">Execution Result</span>
                <span className={`text-xs px-2 py-1 rounded-full ${
                  result.status === 'completed' 
                    ? 'bg-green-500/20 text-green-400 border border-green-500/30'
                    : 'bg-red-500/20 text-red-400 border border-red-500/30'
                }`}>
                  {result.status}
                </span>
                {result.execution_time && (
                  <span className="text-xs text-gray-500">{result.execution_time}ms</span>
                )}
              </div>
              
              {result.output && (
                <pre className="text-sm bg-gray-900/50 p-3 rounded border border-gray-700/30 text-green-300 mb-2">
                  {result.output}
                </pre>
              )}
              
              {result.error && (
                <pre className="text-sm bg-red-900/20 p-3 rounded border border-red-500/30 text-red-300">
                  {result.error}
                </pre>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  const CosmicMessageBubble = ({ message, isStreaming = false }) => {
    const isUser = message.role === 'user';

    return (
      <div className={`flex ${isUser ? 'justify-end' : 'justify-start'} mb-6`}>
        <div className={`flex max-w-4xl ${isUser ? 'flex-row-reverse' : 'flex-row'} space-x-3`}>
          <div className={`flex-shrink-0 ${isUser ? 'ml-3' : 'mr-3'}`}>
            <div className={`w-10 h-10 rounded-full flex items-center justify-center relative ${
              isUser 
                ? 'bg-gradient-to-br from-cyan-500 to-blue-600' 
                : 'bg-gradient-to-br from-purple-500 to-pink-600'
            }`}>
              {isUser ? <User size={18} className="text-white" /> : <Brain size={18} className="text-white" />}
              {!isUser && (
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full blur-lg opacity-50 animate-pulse"></div>
              )}
            </div>
          </div>

          <div className={`flex flex-col ${isUser ? 'items-end' : 'items-start'}`}>
            <div className={`rounded-2xl px-6 py-4 max-w-none relative overflow-hidden ${
              isUser
                ? 'bg-gradient-to-br from-cyan-500/90 to-blue-600/90 text-white backdrop-blur-sm'
                : 'bg-gray-800/80 backdrop-blur-sm text-gray-100 border border-gray-700/50'
            }`}>
              {!isUser && (
                <div className="absolute inset-0 bg-gradient-to-br from-purple-500/5 to-pink-500/5"></div>
              )}
              
              <div className="relative z-10">
                {isUser ? (
                  <div className="whitespace-pre-wrap">{message.content}</div>
                ) : (
                  <div className="prose prose-sm prose-invert max-w-none">
                    {message.content.split('\n').map((line, index) => {
                      if (line.startsWith('# ')) {
                        return <h1 key={index} className="text-xl font-bold text-cyan-400 mb-2">{line.slice(2)}</h1>;
                      }
                      if (line.startsWith('**') && line.endsWith('**')) {
                        return <p key={index} className="font-semibold text-purple-300 mb-2">{line.slice(2, -2)}</p>;
                      }
                      if (line.startsWith('```python')) {
                        return null;
                      }
                      if (line === '```') {
                        return null;
                      }
                      if (line.includes('def ') || line.includes('return ') || line.includes('print(')) {
                        return <CosmicCodeBlock key={index} code={line} language="python" />;
                      }
                      if (line.startsWith('- ')) {
                        return <p key={index} className="mb-1 text-gray-300">• {line.slice(2)}</p>;
                      }
                      return line ? <p key={index} className="mb-2">{line}</p> : <br key={index} />;
                    })}
                    
                    {isStreaming && (
                      <span className="inline-block w-2 h-5 bg-cyan-400 animate-pulse ml-1" />
                    )}
                  </div>
                )}
              </div>
            </div>

            <div className="text-xs text-gray-500 mt-2 flex items-center space-x-1">
              {!isUser && <Sparkles size={12} className="text-purple-400" />}
              <span>
                {message.timestamp && new Date(message.timestamp).toLocaleTimeString()}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  if (!session) {
    return (
      <div className="flex items-center justify-center h-full relative">
        <div className="text-center relative z-10">
          <div className="w-24 h-24 mx-auto mb-6 relative">
            <div className="w-24 h-24 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
              <Brain size={40} className="text-white" />
            </div>
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full blur-xl opacity-50 animate-ping"></div>
          </div>
          <h2 className="text-3xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4">
            Welcome to Neural Space
          </h2>
          <p className="text-gray-400 mb-8 text-lg">Initialize your consciousness interface</p>
          <p className="text-gray-500 text-sm mb-4">
            Create a new session to start chatting with AI models
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="flex flex-col h-full bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-gray-700/30 shadow-2xl overflow-hidden">
      <div className="flex items-center justify-between p-6 border-b border-gray-700/50 bg-gray-800/50 backdrop-blur-sm">
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-3">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
              <Brain size={16} className="text-white" />
            </div>
            <h1 className="text-xl font-semibold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent">
              {session.title}
            </h1>
          </div>
          
          <div className="flex items-center space-x-2 text-sm text-gray-400">
            <div className="w-2 h-2 bg-green-400 rounded-full animate-pulse"></div>
            <span>Neural link active</span>
            <span>•</span>
            <span>{session.model_name || 'Quantum-GPT'}</span>
          </div>
        </div>
        
        <div className="flex items-center space-x-2">
          <button
            onClick={loadModels}
            className="p-2 text-gray-400 hover:text-cyan-400 rounded-lg hover:bg-gray-800/50 transition-all"
            title="Refresh Models"
          >
            <RefreshCw size={18} />
          </button>
        </div>
      </div>

      <div className="flex-1 overflow-y-auto p-6 space-y-6">
        {messages.map((msg) => (
          <CosmicMessageBubble
            key={msg.id}
            message={msg}
          />
        ))}
        
        {isStreaming && streamingContent && (
          <CosmicMessageBubble
            message={{
              role: 'assistant',
              content: streamingContent,
              timestamp: new Date().toISOString()
            }}
            isStreaming={true}
          />
        )}
        
        {isLoading && !isStreaming && (
          <div className="flex items-center space-x-3 text-gray-400">
            <div className="w-8 h-8 bg-gradient-to-br from-purple-500 to-pink-600 rounded-full flex items-center justify-center">
              <Orbit className="animate-spin" size={16} />
            </div>
            <span>Neural pathways processing...</span>
          </div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      <div className="border-t border-gray-700/50 p-6 bg-gray-800/30 backdrop-blur-sm">
        <div className="flex items-end space-x-4">
          <div className="flex-1 relative">
            <textarea
              value={message}
              onChange={(e) => setMessage(e.target.value)}
              onKeyPress={(e) => {
                if (e.key === 'Enter' && !e.shiftKey) {
                  e.preventDefault();
                  handleSend();
                }
              }}
              placeholder="Channel your thoughts into the neural interface..."
              className="w-full px-4 py-3 bg-gray-900/50 border border-gray-600/50 rounded-xl text-gray-100 placeholder-gray-500 focus:outline-none focus:ring-2 focus:ring-cyan-500/50 focus:border-transparent resize-none backdrop-blur-sm"
              rows={Math.min(4, Math.max(1, message.split('\n').length))}
            />
            
            <div className="absolute bottom-2 right-2 text-xs text-gray-500">
              Shift+Enter for new line
            </div>
          </div>
          
          <button
            onClick={handleSend}
            disabled={!message.trim() || isLoading}
            className="p-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-xl hover:from-cyan-400 hover:to-purple-500 disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-300 transform hover:scale-105 shadow-lg shadow-cyan-500/25"
          >
            {isLoading ? (
              <Orbit className="animate-spin" size={20} />
            ) : (
              <Send size={20} />
            )}
          </button>
        </div>
      </div>
    </div>
  );
}

// Placeholder components for other modes
function CosmicSplitScreen({ onExecuteCode }) {
  const [code, setCode] = useState(`# Welcome to the Reality Manipulation Chamber
# Your thoughts become code, code becomes reality

def quantum_hello():
    print("🌌 Initializing quantum consciousness...")
    dimensions = ["Reality", "Digital", "Neural", "Cosmic", "Infinite"]
    
    for i, dimension in enumerate(dimensions):
        print(f"Accessing {dimension} dimension... ✨")
    
    print("🚀 Consciousness fully loaded!")
    return "Reality manipulation complete!"

# Execute this to begin your journey
result = quantum_hello()
print(f"\\nResult: {result}")`);
  
  const [output, setOutput] = useState('');
  const [isExecuting, setIsExecuting] = useState(false);

  const executeCode = async () => {
    if (!code.trim() || isExecuting) return;
    
    setIsExecuting(true);
    try {
      const result = await onExecuteCode(code, 'python');
      setOutput(result.output || result.error || 'No output');
    } catch (error) {
      setOutput(`Error: ${error.message}`);
    } finally {
      setIsExecuting(false);
    }
  };

  return (
    <div className="flex h-full bg-gray-900/40 backdrop-blur-sm rounded-2xl border border-gray-700/30 overflow-hidden">
      <div className="flex-1 flex flex-col">
        <div className="p-4 bg-gray-800/50 border-b border-gray-700/50 flex items-center justify-between">
          <h3 className="text-lg font-semibold text-cyan-400">Reality Manipulation Chamber</h3>
          <button
            onClick={executeCode}
            disabled={isExecuting}
            className="px-4 py-2 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg hover:from-cyan-400 hover:to-purple-500 disabled:opacity-50 flex items-center space-x-2"
          >
            {isExecuting ? (
              <Orbit className="animate-spin" size={16} />
            ) : (
              <Play size={16} />
            )}
            <span>Execute Reality</span>
          </button>
        </div>
        
        <div className="flex-1 p-4">
          <textarea
            value={code}
            onChange={(e) => setCode(e.target.value)}
            className="w-full h-full bg-gray-900/50 border border-gray-600/50 rounded-lg p-4 text-gray-100 font-mono text-sm resize-none focus:outline-none focus:ring-2 focus:ring-cyan-500/50"
            placeholder="// Your thoughts become reality here..."
          />
        </div>
      </div>
      
      <div className="w-1/2 border-l border-gray-700/50 flex flex-col">
        <div className="p-4 bg-gray-800/50 border-b border-gray-700/50">
          <h3 className="text-lg font-semibold text-purple-400">Quantum Output</h3>
        </div>
        
        <div className="flex-1 p-4">
          <pre className="w-full h-full bg-gray-900/50 border border-gray-600/50 rounded-lg p-4 text-green-300 font-mono text-sm overflow-auto">
            {output || 'Execute code to see the magic happen... ✨'}
          </pre>
        </div>
      </div>
    </div>
  );
}

function CosmicSnippetLibrary() {
  return (
    <div className="flex items-center justify-center h-full">
      <div className="text-center">
        <div className="w-24 h-24 mx-auto mb-6 relative">
          <div className="w-24 h-24 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-full flex items-center justify-center animate-pulse">
            <BookOpen size={40} className="text-white" />
          </div>
          <div className="absolute inset-0 bg-gradient-to-br from-emerald-400 to-teal-600 rounded-full blur-xl opacity-50 animate-ping"></div>
        </div>
        <h2 className="text-3xl font-bold bg-gradient-to-r from-emerald-400 to-teal-400 bg-clip-text text-transparent mb-4">
          Memory Bank
        </h2>
        <p className="text-gray-400 mb-8 text-lg">
          Code DNA Repository coming online...
        </p>
      </div>
    </div>
  );
}

// Main Application
export default function CompleteCosmicFrontend() {
  const [currentMode, setCurrentMode] = useState('chat');
  const [currentTheme, setCurrentTheme] = useState('dark');
  const [sidebarCollapsed, setSidebarCollapsed] = useState(false);
  const [sessions, setSessions] = useState([]);
  const [currentSession, setCurrentSession] = useState(null);
  const [isLoading, setIsLoading] = useState(true);
  const [connectionStatus, setConnectionStatus] = useState('connecting');

  useEffect(() => {
    initializeApp();
  }, []);

  const initializeApp = async () => {
    try {
      // Check backend health
      await api.healthCheck();
      setConnectionStatus('connected');
      
      // Load initial data
      await loadSessions();
      
    } catch (error) {
      console.error('Failed to connect to backend:', error);
      setConnectionStatus('error');
    } finally {
      setIsLoading(false);
    }
  };

  const loadSessions = async () => {
    try {
      const sessionsData = await api.getChatSessions();
      const sessionList = sessionsData.sessions || [];
  
      setSessions(sessionList);
  
      if (sessionList.length > 0) {
        setCurrentSession(sessionList[0]);
      }
    } catch (error) {
      console.error('Failed to load sessions:', error);
    }
  };

  const handleModeChange = (mode) => {
    setCurrentMode(mode);
  };

  const handleNewSession = async () => {
    try {
      setIsLoading(true);
      
      // Get available models first
      const modelsResponse = await api.getAvailableModels();
      const models = modelsResponse.models || [];
      
      if (models.length === 0) {
        alert('No models available. Please install Ollama and pull some models, or add your OpenAI API key.');
        return;
      }
      
      const newSession = await api.createChatSession(
        `Neural Session ${Date.now()}`,
        models[0].id
      );
      
      setSessions(prev => [newSession, ...prev]);
      setCurrentSession(newSession);
      
    } catch (error) {
      console.error('Failed to create session:', error);
      alert('Failed to create session. Please check if the backend is running.');
    } finally {
      setIsLoading(false);
    }
  };

  const handleSessionSwitch = async (sessionId) => {
    try {
      const session = await api.getChatSession(sessionId);
      setCurrentSession(session);
    } catch (error) {
      console.error('Failed to switch session:', error);
    }
  };

  const handleExecuteCode = async (code, language, files) => {
    try {
      const result = await api.executeCode(code, language, files, currentSession?.id);
      return result;
    } catch (error) {
      console.error('Code execution failed:', error);
      throw error;
    }
  };

  const renderCurrentMode = () => {
    switch (currentMode) {
      case 'chat':
        return (
          <CosmicChatInterface
            session={currentSession}
            onExecuteCode={handleExecuteCode}
          />
        );
      case 'code':
        return (
          <CosmicSplitScreen
            onExecuteCode={handleExecuteCode}
          />
        );
      case 'snippets':
        return <CosmicSnippetLibrary />;
      default:
        return (
          <CosmicChatInterface
            session={currentSession}
            onExecuteCode={handleExecuteCode}
          />
        );
    }
  };

  if (isLoading) {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 mx-auto mb-6 relative">
            <div className="w-24 h-24 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full flex items-center justify-center animate-pulse">
              <Brain size={40} className="text-white" />
            </div>
            <div className="absolute inset-0 bg-gradient-to-br from-cyan-400 to-purple-600 rounded-full blur-xl opacity-50 animate-ping"></div>
          </div>
          <h2 className="text-2xl font-bold bg-gradient-to-r from-cyan-400 to-purple-400 bg-clip-text text-transparent mb-4">
            Initializing Cosmic Interface
          </h2>
          <p className="text-gray-400">Connecting to neural networks...</p>
        </div>
      </div>
    );
  }

  if (connectionStatus === 'error') {
    return (
      <div className="min-h-screen bg-black flex items-center justify-center">
        <div className="text-center">
          <div className="w-24 h-24 mx-auto mb-6 relative">
            <div className="w-24 h-24 bg-gradient-to-br from-red-500 to-pink-600 rounded-full flex items-center justify-center">
              <AlertCircle size={40} className="text-white" />
            </div</div>
          <h2 className="text-2xl font-bold text-red-400 mb-4">
            Neural Link Disrupted
          </h2>
          <p className="text-gray-400 mb-6">
            Cannot connect to the backend server at http://localhost:8001
          </p>
          <div className="bg-gray-800/50 rounded-lg p-4 text-left max-w-md mx-auto">
            <h3 className="text-sm font-semibold text-gray-200 mb-2">To fix this:</h3>
            <ol className="text-sm text-gray-400 space-y-1">
              <li>1. Start the backend server</li>
              <li>2. Run: <code className="bg-gray-900/50 px-1 rounded">python run.py</code></li>
              <li>3. Ensure it's running on port 8000</li>
              <li>4. Refresh this page</li>
            </ol>
          </div>
          <button
            onClick={() => window.location.reload()}
            className="mt-6 px-6 py-3 bg-gradient-to-r from-cyan-500 to-purple-600 text-white rounded-lg hover:from-cyan-400 hover:to-purple-500 transition-all"
          >
            Retry Connection
          </button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-black text-white overflow-hidden">
      <CosmicLayout
        theme={currentTheme}
        onThemeToggle={() => setCurrentTheme(currentTheme === 'dark' ? 'light' : 'dark')}
        sidebarCollapsed={sidebarCollapsed}
        onSidebarToggle={() => setSidebarCollapsed(!sidebarCollapsed)}
        sessions={sessions}
        currentSession={currentSession}
        onSessionSwitch={handleSessionSwitch}
        onNewSession={handleNewSession}
        currentMode={currentMode}
        onModeChange={handleModeChange}
      >
        {renderCurrentMode()}
      </CosmicLayout>

      {/* Cosmic Status Indicators */}
      <div className="fixed bottom-4 left-4 space-y-2 z-50">
        <div className={`bg-gray-900/80 backdrop-blur-xl rounded-lg px-3 py-2 border ${
          connectionStatus === 'connected' ? 'border-green-500/30' : 'border-red-500/30'
        }`}>
          <div className="flex items-center space-x-2 text-sm">
            <div className={`w-2 h-2 rounded-full animate-pulse ${
              connectionStatus === 'connected' ? 'bg-green-400' : 'bg-red-400'
            }`}></div>
            <span className={connectionStatus === 'connected' ? 'text-green-400' : 'text-red-400'}>
              Backend: {connectionStatus === 'connected' ? 'Connected' : 'Disconnected'}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-900/80 backdrop-blur-xl rounded-lg px-3 py-2 border border-cyan-500/30">
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-cyan-400 rounded-full animate-pulse"></div>
            <span className="text-cyan-400">
              Sessions: {sessions.length}
            </span>
          </div>
        </div>
        
        <div className="bg-gray-900/80 backdrop-blur-xl rounded-lg px-3 py-2 border border-purple-500/30">
          <div className="flex items-center space-x-2 text-sm">
            <div className="w-2 h-2 bg-purple-400 rounded-full animate-pulse"></div>
            <span className="text-purple-400">
              Mode: {currentMode}
            </span>
          </div>
        </div>
      </div>
    </div>
  );
}