from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from typing import Dict, Any
import subprocess
import tempfile
import os

router = APIRouter()

class ExecuteRequest(BaseModel):
    code: str
    language: str = "python"

@router.post("/execute/run")
def execute_code(request: ExecuteRequest):
    """Execute code in a sandboxed environment"""
    
    if request.language.lower() != "python":
        raise HTTPException(status_code=400, detail="Only Python execution is currently supported")
    
    try:
        # Create a temporary file
        with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False) as f:
            f.write(request.code)
            temp_file = f.name
        
        # Execute the code
        result = subprocess.run(
            ["python3", temp_file],
            capture_output=True,
            text=True,
            timeout=10  # 10 second timeout
        )
        
        # Clean up
        os.unlink(temp_file)
        
        return {
            "output": result.stdout,
            "error": result.stderr,
            "return_code": result.returncode,
            "success": result.returncode == 0
        }
        
    except subprocess.TimeoutExpired:
        # Clean up
        if 'temp_file' in locals():
            os.unlink(temp_file)
        raise HTTPException(status_code=408, detail="Code execution timed out")
    
    except Exception as e:
        # Clean up
        if 'temp_file' in locals():
            os.unlink(temp_file)
        raise HTTPException(status_code=500, detail=f"Execution error: {str(e)}")
