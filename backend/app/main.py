from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from app.routers import chat
from app.api import models, execute

app = FastAPI()

# Middleware for CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # You can restrict this later
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Health check route
@app.get("/health")
def health_check():
    return {"status": "ok"}

# Favicon route to prevent 404 errors
@app.get("/favicon.ico")
def favicon():
    return {"message": "No favicon configured"}

# Mount routers
app.include_router(chat.router, prefix="/api/v1")
app.include_router(models.router, prefix="/api/v1")
app.include_router(execute.router, prefix="/api/v1")
