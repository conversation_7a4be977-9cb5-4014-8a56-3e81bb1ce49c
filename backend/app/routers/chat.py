from fastapi import APIRouter, HTTPException
from pydantic import BaseModel
from uuid import uuid4
from datetime import datetime
from typing import List, Optional

router = APIRouter()

sessions = []
messages = {}  # Dictionary to store messages by session_id

class SessionRequest(BaseModel):
    title: str
    model_name: str

class MessageRequest(BaseModel):
    content: str
    role: str = "user"

class Message(BaseModel):
    id: str
    session_id: str
    role: str
    content: str
    timestamp: str

@router.get("/chat/sessions")
def get_sessions():
    return sessions

@router.post("/chat/sessions")
def create_session(request: SessionRequest):
    session = {
        "id": str(uuid4()),
        "title": request.title,
        "model_name": request.model_name,
        "updated_at": datetime.utcnow().isoformat()
    }
    sessions.append(session)
    # Initialize empty messages list for this session
    messages[session["id"]] = []
    return session

@router.get("/chat/sessions/{session_id}/messages")
def get_messages(session_id: str):
    """Get all messages for a specific chat session"""
    if session_id not in messages:
        raise HTTPException(status_code=404, detail="Session not found")
    return {"messages": messages[session_id]}

@router.post("/chat/sessions/{session_id}/messages")
def create_message(session_id: str, request: MessageRequest):
    """Add a new message to a chat session"""
    if session_id not in messages:
        raise HTTPException(status_code=404, detail="Session not found")
    
    # Create user message
    user_message = {
        "id": str(uuid4()),
        "session_id": session_id,
        "role": request.role,
        "content": request.content,
        "timestamp": datetime.utcnow().isoformat()
    }
    
    messages[session_id].append(user_message)
    
    # For now, create a simple AI response
    ai_message = {
        "id": str(uuid4()),
        "session_id": session_id,
        "role": "assistant",
        "content": f"I received your message: '{request.content}'. This is a placeholder response.",
        "timestamp": datetime.utcnow().isoformat()
    }
    
    messages[session_id].append(ai_message)
    
    return ai_message
