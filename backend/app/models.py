from fastapi import APIRouter
from typing import List

router = APIRouter()

# Available AI models
AVAILABLE_MODELS = [
    {
        "id": "gpt-3.5-turbo",
        "name": "GPT-3.5 Turbo",
        "provider": "OpenAI",
        "description": "Fast and efficient model for most tasks"
    },
    {
        "id": "gpt-4",
        "name": "GPT-4",
        "provider": "OpenAI",
        "description": "Most capable model for complex reasoning"
    },
    {
        "id": "claude-3-sonnet",
        "name": "Claude 3 Sonnet",
        "provider": "Anthropic",
        "description": "Balanced model for various tasks"
    },
    {
        "id": "claude-3-opus",
        "name": "Claude 3 Opus",
        "provider": "Anthropic",
        "description": "Most powerful model for complex tasks"
    }
]

@router.get("/models/available")
def get_available_models():
    """Get list of available AI models"""
    return {"models": AVAILABLE_MODELS}
