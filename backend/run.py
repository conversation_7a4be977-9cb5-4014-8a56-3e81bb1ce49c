from fastapi import APIRouter
from pydantic import BaseModel
from uuid import uuid4
from datetime import datetime

router = APIRouter()

# Dummy session DB
sessions = []

class SessionRequest(BaseModel):
    title: str
    model_name: str

@router.get("/chat/sessions")
def get_sessions():
    return sessions

@router.post("/chat/sessions")
def create_session(request: SessionRequest):
    session = {
        "id": str(uuid4()),
        "title": request.title,
        "model_name": request.model_name,
        "updated_at": datetime.utcnow().isoformat()
    }
    sessions.append(session)
    return session
